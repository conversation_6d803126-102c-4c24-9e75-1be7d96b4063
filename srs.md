# 🧾 Software Requirements Specification (SRS)

## Project Name: Cursor AI – KMS Demo  
**Version:** 0.1 (MVP)  
**Date:** July 2025

---

## 1. Purpose  
Deliver a minimal but functional Knowledge Management System (KMS) with Retrieval-Augmented Generation (RAG) capabilities. It enables users to upload documents, store their content, and ask questions, receiving AI-generated answers with source references using Gemini API.

---

## 2. Scope  
This MVP includes:
- Uploading and parsing documents
- Storing chunked text + embeddings in SQLite
- Semantic search of knowledge base
- Using Gemini for context-based answer generation
- A simple front-end with chat-style Q&A

---

## 3. Tech Stack  
- **Frontend**: Next.js (React, Tailwind CSS)  
- **Backend**: Next.js API routes (Node.js)  
- **Database**: SQLite  
- **AI Engine**: Gemini API (Google AI)  
- **Embeddings**: Gemini or custom (e.g., Universal Sentence Encoder)

---

## 4. Core Features (MVP)

### 4.1 Document Upload  
- Upload support for PDF, TXT, DOCX  
- Extract and chunk document content  
- Store text and metadata in SQLite  
- Generate and store embeddings  

### 4.2 Semantic Search  
- Accept natural language user query  
- Convert to embedding  
- Retrieve top N most similar chunks via cosine similarity  
- Return top 3–5 relevant results  

### 4.3 AI Answer Generation  
- Combine user question with retrieved chunks  
- Prompt Gemini API  
- Display answer + context references  

### 4.4 Frontend UI  
- Document upload interface (admin only)  
- Chat-style Q&A interface  
- Answer with references  
- Loading/error indicators

---

## 5. Non-Functional Requirements  
- Responsive UI  
- Fast response time (<2s ideally)  
- Secure storage of Gemini API key  
- Simple local setup (no cloud infra required)

---

## 6. User Roles  
- **Admin**: Upload documents  
- **User**: Ask questions and receive answers

---

## 7. Future Scope (Not in MVP)  
- Role-based user access  
- Chat history and analytics  
- E-learning content and course tracking  
- Multimedia (images, video, PPT support)  
- External system integrations (Google Drive, Notion, etc.)


