# 🧾 Knowledge Management System (KMS) Demo

A modern Knowledge Management System with RAG (Retrieval-Augmented Generation) capabilities built with Next.js and Gemini AI.

## 🚀 Features

- **Document Upload**: Support for PDF, TXT, and DOCX files
- **Intelligent Chunking**: Automatic text chunking with overlap for better context
- **Semantic Search**: Vector embeddings for semantic similarity search
- **AI-Powered Q&A**: RAG-based question answering with source references
- **Modern UI**: Clean, responsive interface built with Tailwind CSS
- **Real-time Chat**: Interactive chat interface with loading states
- **Source Attribution**: Answers include similarity scores and source references

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, Tailwind CSS
- **Backend**: Next.js API Routes, Node.js
- **Database**: SQLite with custom schema
- **AI**: Google Gemini API for embeddings and text generation
- **Document Processing**: PDF-parse, Mammoth (DOCX), native text processing
- **Testing**: Jest with TypeScript support

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Google Gemini API key (get from [Google AI Studio](https://makersuite.google.com/app/apikey))

## 🔧 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kms-demo
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```
   Edit `.env.local` and add your Gemini API key:
   ```
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🎯 Usage

### Document Upload
1. Click on the document upload area
2. Select a PDF, TXT, or DOCX file (max 10MB)
3. Click "Upload Document"
4. Wait for processing and chunking to complete

### Ask Questions
1. Type your question in the chat interface
2. Click "Ask" or press Enter
3. View the AI-generated answer with source references
4. Check similarity scores to understand relevance

### Example Questions
- "What is the main topic of the document?"
- "Summarize the key points"
- "What are the technical requirements?"
- "How does the system work?"

## 🧪 Testing

Run the test suite:
```bash
npm test
```

Run tests in watch mode:
```bash
npm run test:watch
```

## 📁 Project Structure

```
src/
├── app/
│   ├── api/
│   │   ├── upload/     # Document upload endpoint
│   │   ├── chat/       # Chat/Q&A endpoint
│   │   └── documents/  # Document listing endpoint
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   ├── DocumentUpload.tsx
│   └── ChatInterface.tsx
├── lib/
│   ├── database.ts     # SQLite database manager
│   └── gemini.ts       # Gemini AI service
├── types/
│   └── database.ts     # TypeScript interfaces
├── utils/
│   └── documentProcessor.ts  # Document processing utilities
└── __tests__/
    └── documentProcessor.test.ts
```

## 🗄️ Database Schema

### Documents Table
- `id`: Primary key
- `filename`: Original filename
- `content`: Full document text
- `file_type`: MIME type
- `upload_date`: Timestamp
- `file_size`: File size in bytes

### Document Chunks Table
- `id`: Primary key
- `document_id`: Foreign key to documents
- `chunk_text`: Text chunk content
- `chunk_index`: Order within document
- `embedding`: JSON-encoded vector embedding
- `created_at`: Timestamp

## 🔍 How It Works

1. **Document Processing**: Files are parsed and split into overlapping chunks
2. **Embedding Generation**: Each chunk gets a vector embedding using Gemini
3. **Storage**: Chunks and embeddings are stored in SQLite
4. **Query Processing**: User questions are converted to embeddings
5. **Similarity Search**: Cosine similarity finds relevant chunks
6. **Answer Generation**: Gemini generates answers using retrieved context
7. **Response**: Answer is returned with source references and similarity scores

## 🚀 Deployment

### Local Production Build
```bash
npm run build
npm start
```

### Environment Variables for Production
- `GEMINI_API_KEY`: Your Gemini API key
- `NODE_ENV`: Set to "production"

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Run the test suite
6. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Troubleshooting

### Common Issues

1. **Database not found**: The SQLite database is created automatically in the `data/` directory
2. **Gemini API errors**: Check your API key and ensure it's valid
3. **File upload fails**: Ensure file size is under 10MB and format is supported
4. **No answers generated**: Make sure documents are uploaded and processed first

### Support

For issues and questions, please check the existing issues or create a new one in the repository.

## 🔮 Future Enhancements

- User authentication and role-based access
- Chat history persistence
- Advanced document analytics
- Multi-language support
- Integration with external document sources
- Advanced embedding models
- Real-time collaborative features
