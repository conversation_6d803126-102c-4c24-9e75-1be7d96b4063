'use client';

import { useState, useEffect } from 'react';
import { Document } from '../types/database';
import { TrashIcon } from '@heroicons/react/24/outline';

export function DocumentUpload() {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [message, setMessage] = useState('');
  const [documents, setDocuments] = useState<Document[]>([]);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<number | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      setMessage('');
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setMessage('Please select a file first');
      return;
    }

    setUploading(true);
    setMessage('');

    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        setMessage(`✅ ${result.message} (${result.chunksCount} chunks created)`);
        setFile(null);
        // Reset file input
        const fileInput = document.getElementById('file-input') as HTMLInputElement;
        if (fileInput) fileInput.value = '';
        
        // Refresh documents list
        fetchDocuments();
      } else {
        setMessage(`❌ Error: ${result.error}`);
      }
    } catch (error) {
      setMessage('❌ Upload failed. Please try again.');
      console.error('Upload error:', error);
    } finally {
      setUploading(false);
    }
  };

  const fetchDocuments = async () => {
    try {
      const response = await fetch('/api/documents');
      const result = await response.json();
      if (response.ok) {
        setDocuments(result.documents);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    }
  };

  const handleDelete = async (documentId: number) => {
    setDeletingId(documentId);
    setMessage('');

    try {
      const response = await fetch(`/api/documents/${documentId}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (response.ok) {
        setMessage(`✅ ${result.message}`);
        // Refresh documents list
        fetchDocuments();
      } else {
        setMessage(`❌ Error: ${result.error}`);
      }
    } catch (error) {
      setMessage('❌ Delete failed. Please try again.');
      console.error('Delete error:', error);
    } finally {
      setDeletingId(null);
      setShowDeleteConfirm(null);
    }
  };

  const confirmDelete = (documentId: number) => {
    setShowDeleteConfirm(documentId);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(null);
  };

  const getFileTypeLabel = (mimeType: string) => {
    const typeMap: { [key: string]: string } = {
      'application/pdf': 'PDF',
      'text/plain': 'TXT',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
      'application/msword': 'DOC',
    };
    return typeMap[mimeType] || mimeType.split('/').pop()?.toUpperCase() || 'UNKNOWN';
  };

  // Fetch documents on component mount
  useEffect(() => {
    fetchDocuments();
  }, []);

  return (
    <div className="space-y-4">
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
        <div className="space-y-4">
          <div className="text-gray-600">
            <p className="text-sm">Supported formats: PDF, TXT, DOCX</p>
            <p className="text-xs text-gray-500">Max file size: 10MB</p>
          </div>
          
          <input
            id="file-input"
            type="file"
            accept=".pdf,.txt,.docx"
            onChange={handleFileChange}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-[#63ae45] file:text-white hover:file:bg-[#5a9c3e]"
          />
          
          {file && (
            <div className="text-sm text-gray-700">
              Selected: {file.name} ({(file.size / 1024).toFixed(1)} KB)
            </div>
          )}
          
          <button
            onClick={handleUpload}
            disabled={!file || uploading}
            className="w-full bg-[#224d59] text-white py-2 px-4 rounded-lg font-medium hover:bg-[#1e4651] disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            {uploading ? 'Uploading...' : 'Upload Document'}
          </button>
        </div>
      </div>
      
      {message && (
        <div className={`p-3 rounded-lg text-sm ${
          message.includes('✅') ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
        }`}>
          {message}
        </div>
      )}
      
      {documents.length > 0 && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-3 text-[#224d59]">Uploaded Documents</h3>
          <div className="space-y-2">
            {documents.map((doc) => (
              <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border-l-2 border-[#63ae45]">
                  <div className="flex-1">
                    <div className="font-medium text-sm text-gray-900">{doc.filename}</div>
                  <div className="text-xs text-gray-500">
                    {new Date(doc.upload_date).toLocaleDateString()} • {(doc.file_size / 1024).toFixed(1)} KB
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="text-xs text-[#63ae45] font-medium">
                    {getFileTypeLabel(doc.file_type)}
                  </div>
                  <button
                    onClick={() => confirmDelete(doc.id)}
                    disabled={deletingId === doc.id}
                    className="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors disabled:opacity-50"
                    title="Delete document"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Delete Document</h3>
            <p className="text-gray-600 mb-4">
              Are you sure you want to delete this document? This action cannot be undone.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => handleDelete(showDeleteConfirm)}
                disabled={deletingId === showDeleteConfirm}
                className="flex-1 bg-red-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-red-700 disabled:bg-red-300 disabled:cursor-not-allowed transition-colors"
              >
                {deletingId === showDeleteConfirm ? 'Deleting...' : 'Delete'}
              </button>
              <button
                onClick={cancelDelete}
                disabled={deletingId === showDeleteConfirm}
                className="flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-lg font-medium hover:bg-gray-300 disabled:opacity-50 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 