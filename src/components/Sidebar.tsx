'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { DocumentIcon, ChatBubbleLeftIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import Image from 'next/image';
import { useAuth } from '../contexts/AuthContext';
import { UserProfile } from './UserProfile';

export function Sidebar() {
  const pathname = usePathname();
  const { user, canUploadDocuments } = useAuth();

  // Base navigation items
  const baseNavigation = [
    {
      name: 'Ask Questions',
      href: '/chat',
      icon: ChatBubbleLeftIcon,
      description: 'Chat with your documents',
      requiresAuth: false
    }
  ];

  // Admin-only navigation items
  const adminNavigation = [
    {
      name: 'Document Upload',
      href: '/upload',
      icon: DocumentIcon,
      description: 'Upload and process documents',
      requiresAuth: true,
      adminOnly: true
    }
  ];

  // Combine navigation based on user role
  const navigation = [
    ...baseNavigation,
    ...(canUploadDocuments() ? adminNavigation : [])
  ];

  return (
    <div className="flex flex-col w-64 bg-[#224d59] shadow-lg">
      <div className="flex flex-col h-0 flex-1">
        {/* Logo Header */}
        <div className="flex flex-col items-center h-24 flex-shrink-0 px-4 py-4 bg-[#224d59]">
          <Image
            src="/logo-white.svg"
            alt="KMS Logo"
            width={100}
            height={100}
            className="mb-2"
          />
          <div className="text-center">
            <h1 className="text-white text-lg font-bold">Knowledge Management</h1>
          </div>
        </div>
        
        {/* Navigation */}
        <div className="flex-1 flex flex-col overflow-y-auto bg-[#224d59]">
          <nav className="flex-1 px-2 py-4 space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`
                    group flex items-center px-3 py-3 text-sm font-medium rounded-md transition-colors
                    ${isActive
                      ? 'bg-[#63ae45] text-white shadow-sm'
                      : 'text-gray-200 hover:bg-[#1e4651] hover:text-white'
                    }
                  `}
                >
                  <item.icon
                    className={`
                      mr-3 flex-shrink-0 h-6 w-6
                      ${isActive ? 'text-white' : 'text-gray-300 group-hover:text-white'}
                    `}
                    aria-hidden="true"
                  />
                  <div className="flex flex-col">
                    <span className="font-medium">{item.name}</span>
                    <span className={`text-xs ${isActive ? 'text-gray-100' : 'text-gray-400'}`}>
                      {item.description}
                    </span>
                  </div>
                </Link>
              );
            })}
          </nav>
          
          {/* User Profile */}
          {user && (
            <div className="px-2 py-3 border-t border-gray-600">
              <UserProfile />
            </div>
          )}

          {/* Footer */}
          <div className="px-4 py-3 border-t border-gray-600">
            <div className="flex items-center justify-center space-x-2">
              <ShieldCheckIcon className="w-4 h-4 text-gray-300" />
              <p className="text-xs text-gray-300">
                AI-Powered Knowledge System
              </p>
            </div>
            {user && (
              <p className="text-xs text-gray-400 text-center mt-1">
                Logged in as {user.role}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 