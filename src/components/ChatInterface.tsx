'use client';

import { useState } from 'react';

interface ChatMessage {
  id: string;
  question: string;
  answer: string;
  sources: Array<{
    filename: string;
    similarity: number;
    text: string;
  }>;
  timestamp: Date;
}

export function ChatInterface() {
  const [question, setQuestion] = useState('');
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!question.trim()) return;

    setLoading(true);
    const currentQuestion = question;
    setQuestion('');

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ question: currentQuestion }),
      });

      const result = await response.json();

      if (response.ok) {
        const newMessage: ChatMessage = {
          id: Date.now().toString(),
          question: currentQuestion,
          answer: result.answer,
          sources: result.sources || [],
          timestamp: new Date(),
        };

        setMessages(prev => [...prev, newMessage]);
      } else {
        // Handle error
        const errorMessage: ChatMessage = {
          id: Date.now().toString(),
          question: currentQuestion,
          answer: `Error: ${result.error}`,
          sources: [],
          timestamp: new Date(),
        };
        setMessages(prev => [...prev, errorMessage]);
      }
    } catch (error) {
      console.error('Chat error:', error);
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        question: currentQuestion,
        answer: 'Sorry, there was an error processing your question. Please try again.',
        sources: [],
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-96">
      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto space-y-4 mb-4 p-4 bg-gray-50 rounded-lg">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>Ask a question about your uploaded documents</p>
                         <p className="text-sm mt-2">Example: &quot;What is the main topic of the document?&quot;</p>
          </div>
        ) : (
          messages.map((message) => (
            <div key={message.id} className="space-y-3">
              {/* Question */}
              <div className="flex justify-end">
                <div className="bg-[#224d59] text-white p-3 rounded-lg max-w-xs">
                  <p className="text-sm">{message.question}</p>
                </div>
              </div>
              
              {/* Answer */}
              <div className="flex justify-start">
                <div className="bg-white p-3 rounded-lg max-w-md border border-l-4 border-l-[#63ae45]">
                  <p className="text-sm text-gray-800 mb-2">{message.answer}</p>
                  
                  {/* Sources */}
                  {message.sources.length > 0 && (
                    <div className="mt-3 pt-3 border-t">
                      <p className="text-xs text-gray-600 font-medium mb-2">Sources:</p>
                      <div className="space-y-1">
                        {message.sources.map((source, index) => (
                          <div key={index} className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                            <div className="font-medium">{source.filename}</div>
                            <div className="text-gray-400 mt-1">{source.text}</div>
                            <div className="text-gray-400 mt-1">
                              Similarity: {(source.similarity * 100).toFixed(1)}%
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div className="text-xs text-gray-400 mt-2">
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
        
        {loading && (
          <div className="flex justify-start">
            <div className="bg-white p-3 rounded-lg border border-l-4 border-l-[#63ae45]">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#224d59]"></div>
                <span className="text-sm text-gray-600">Thinking...</span>
              </div>
            </div>
          </div>
        )}
      </div>
      
      {/* Input Form */}
      <form onSubmit={handleSubmit} className="flex space-x-2">
        <input
          type="text"
          value={question}
          onChange={(e) => setQuestion(e.target.value)}
          placeholder="Ask a question about your documents..."
          className="flex-1 p-2 border text-black border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#224d59]"
          disabled={loading}
        />
        <button
          type="submit"
          disabled={loading || !question.trim()}
          className="bg-[#224d59] text-white px-4 py-2 rounded-lg hover:bg-[#1e4651] disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
        >
          {loading ? '...' : 'Ask'}
        </button>
      </form>
    </div>
  );
} 