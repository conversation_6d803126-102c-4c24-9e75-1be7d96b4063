import { GoogleGenerativeAI } from '@google/generative-ai';
import { validateEnvironment } from './envCheck';

class GeminiService {
  private genAI: GoogleGenerativeAI;
  private model: ReturnType<GoogleGenerativeAI['getGenerativeModel']>;

  constructor() {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey || !validateEnvironment()) {
      throw new Error('GEMINI_API_KEY environment variable is required. Please set it in your .env.local file.');
    }
    
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
  }

  async generateEmbedding(text: string): Promise<number[]> {
    try {
      // For now, we'll use a simple text-based embedding approach
      // In a production system, you'd use a proper embedding model
      // This is a simplified approach - in reality, you'd use a proper embedding model
      // For now, we'll create a simple hash-based embedding
      const embedding = this.createSimpleEmbedding(text);
      return embedding;
    } catch (error) {
      console.error('Error generating embedding:', error);
      throw error;
    }
  }

  private createSimpleEmbedding(text: string): number[] {
    // Simple hash-based embedding (for demo purposes)
    // In production, use proper embedding models like text-embedding-004
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(384).fill(0); // 384-dimensional embedding
    
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      let hash = 0;
      for (let j = 0; j < word.length; j++) {
        hash = ((hash << 5) - hash + word.charCodeAt(j)) & 0xffffffff;
      }
      const index = Math.abs(hash) % embedding.length;
      embedding[index] += 1;
    }
    
    // Normalize the embedding
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => magnitude > 0 ? val / magnitude : 0);
  }

  calculateCosineSimilarity(embedding1: number[], embedding2: number[]): number {
    if (embedding1.length !== embedding2.length) {
      throw new Error('Embeddings must have the same length');
    }

    let dotProduct = 0;
    let magnitude1 = 0;
    let magnitude2 = 0;

    for (let i = 0; i < embedding1.length; i++) {
      dotProduct += embedding1[i] * embedding2[i];
      magnitude1 += embedding1[i] * embedding1[i];
      magnitude2 += embedding2[i] * embedding2[i];
    }

    magnitude1 = Math.sqrt(magnitude1);
    magnitude2 = Math.sqrt(magnitude2);

    if (magnitude1 === 0 || magnitude2 === 0) {
      return 0;
    }

    return dotProduct / (magnitude1 * magnitude2);
  }

  async generateAnswer(question: string, context: string[]): Promise<string> {
    try {
      const contextText = context.join('\n\n');
      const prompt = `
        Based on the following context, answer the question. If the answer cannot be found in the context, say "I don't have enough information to answer that question."

        Context:
        ${contextText}

        Question: ${question}

        Answer:
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      return response.text();
    } catch (error) {
      console.error('Error generating answer:', error);
      throw error;
    }
  }
}

// Singleton instance
let geminiInstance: GeminiService | null = null;

export function getGeminiService(): GeminiService {
  if (!geminiInstance) {
    geminiInstance = new GeminiService();
  }
  return geminiInstance;
}

export default GeminiService; 