import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import path from 'path';
import { Document, DocumentChunk, DocumentChunkWithFilename } from '../types/database';

// Enable verbose mode for debugging
const Database = sqlite3.verbose().Database;

class DatabaseManager {
  private db: sqlite3.Database;
  private dbPath: string;

  constructor() {
    this.dbPath = path.join(process.cwd(), 'data', 'kms.db');
    this.db = new Database(this.dbPath);
    this.initializeDatabaseSync();
  }

  private initializeDatabaseSync() {
    try {
      // Create documents table
      this.db.run(`
        CREATE TABLE IF NOT EXISTS documents (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          filename TEXT NOT NULL,
          content TEXT NOT NULL,
          file_type TEXT NOT NULL,
          upload_date DATETIME DEFAULT CURRENT_TIMESTAMP,
          file_size INTEGER
        )
      `);

      // Create document_chunks table for storing text chunks with embeddings
      this.db.run(`
        CREATE TABLE IF NOT EXISTS document_chunks (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          document_id INTEGER NOT NULL,
          chunk_text TEXT NOT NULL,
          chunk_index INTEGER NOT NULL,
          embedding TEXT NOT NULL,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (document_id) REFERENCES documents (id) ON DELETE CASCADE
        )
      `);

      // Create index for faster similarity searches
      this.db.run(`
        CREATE INDEX IF NOT EXISTS idx_document_chunks_document_id 
        ON document_chunks(document_id)
      `);

      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Error initializing database:', error);
      throw error;
    }
  }

  async insertDocument(filename: string, content: string, fileType: string, fileSize: number): Promise<number> {
    return new Promise((resolve, reject) => {
      this.db.run(
        'INSERT INTO documents (filename, content, file_type, file_size) VALUES (?, ?, ?, ?)',
        [filename, content, fileType, fileSize],
        function(err) {
          if (err) {
            console.error('Error inserting document:', err);
            reject(err);
          } else {
            resolve(this.lastID);
          }
        }
      );
    });
  }

  async insertDocumentChunk(documentId: number, chunkText: string, chunkIndex: number, embedding: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db.run(
        'INSERT INTO document_chunks (document_id, chunk_text, chunk_index, embedding) VALUES (?, ?, ?, ?)',
        [documentId, chunkText, chunkIndex, embedding],
        function(err) {
          if (err) {
            console.error('Error inserting document chunk:', err);
            reject(err);
          } else {
            resolve();
          }
        }
      );
    });
  }

  async getAllDocuments(): Promise<Document[]> {
    return new Promise((resolve, reject) => {
      this.db.all('SELECT * FROM documents ORDER BY upload_date DESC', (err, rows) => {
        if (err) {
          console.error('Error getting documents:', err);
          reject(err);
        } else {
          resolve(rows as Document[]);
        }
      });
    });
  }

  async getDocumentChunks(documentId: number): Promise<DocumentChunk[]> {
    return new Promise((resolve, reject) => {
      this.db.all(
        'SELECT * FROM document_chunks WHERE document_id = ? ORDER BY chunk_index',
        [documentId],
        (err, rows) => {
          if (err) {
            console.error('Error getting document chunks:', err);
            reject(err);
          } else {
            resolve(rows as DocumentChunk[]);
          }
        }
      );
    });
  }

  async getAllChunks(): Promise<DocumentChunkWithFilename[]> {
    return new Promise((resolve, reject) => {
      this.db.all(`
        SELECT dc.*, d.filename 
        FROM document_chunks dc 
        JOIN documents d ON dc.document_id = d.id 
        ORDER BY dc.created_at DESC
      `, (err, rows) => {
        if (err) {
          console.error('Error getting all chunks:', err);
          reject(err);
        } else {
          resolve(rows as DocumentChunkWithFilename[]);
        }
      });
    });
  }

  async deleteDocument(documentId: number): Promise<void> {
    return new Promise((resolve, reject) => {
      const db = this.db;
      
      // Start a transaction to ensure both document and chunks are deleted together
      db.serialize(() => {
        db.run('BEGIN TRANSACTION');
        
        // Delete document chunks first (due to foreign key constraint)
        db.run(
          'DELETE FROM document_chunks WHERE document_id = ?',
          [documentId],
          (err) => {
            if (err) {
              console.error('Error deleting document chunks:', err);
              db.run('ROLLBACK');
              reject(err);
              return;
            }
            
            // Delete the document
            db.run(
              'DELETE FROM documents WHERE id = ?',
              [documentId],
              function(err) {
                if (err) {
                  console.error('Error deleting document:', err);
                  db.run('ROLLBACK');
                  reject(err);
                } else if (this.changes === 0) {
                  db.run('ROLLBACK');
                  reject(new Error('Document not found'));
                } else {
                  db.run('COMMIT');
                  resolve();
                }
              }
            );
          }
        );
      });
    });
  }

  async close(): Promise<void> {
    const closeAsync = promisify(this.db.close.bind(this.db));
    await closeAsync();
  }
}

// Singleton instance
let dbInstance: DatabaseManager | null = null;
let isInitializing = false;

export function getDatabase(): DatabaseManager {
  if (!dbInstance && !isInitializing) {
    isInitializing = true;
    dbInstance = new DatabaseManager();
    isInitializing = false;
  }
  return dbInstance!;
}

export default DatabaseManager; 