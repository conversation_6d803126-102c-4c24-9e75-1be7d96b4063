export function validateEnvironment() {
  if (!process.env.GEMINI_API_KEY) {
    console.warn('Warning: GEMINI_API_KEY environment variable is not set. Please add it to your .env.local file.');
    return false;
  }
  
  if (process.env.GEMINI_API_KEY === 'your_gemini_api_key_here') {
    console.warn('Warning: Please replace the placeholder GEMINI_API_KEY with your actual API key.');
    return false;
  }
  
  return true;
} 