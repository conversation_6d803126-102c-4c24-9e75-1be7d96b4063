'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, UserRole, AuthState, AuthContextType, LoginCredentials } from '../types/auth';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Simple demo users - in a real app, this would be in a database
const DEMO_USERS: Array<User & { password: string }> = [
  {
    id: '1',
    username: 'admin',
    password: 'admin123',
    role: 'admin',
    createdAt: new Date('2025-01-01')
  },
  {
    id: '2',
    username: 'user',
    password: 'user123',
    role: 'user',
    createdAt: new Date('2025-01-01')
  },
  {
    id: '3',
    username: 'john.doe',
    password: 'password',
    role: 'user',
    createdAt: new Date('2025-01-01')
  },
  {
    id: '4',
    username: 'jane.admin',
    password: 'admin456',
    role: 'admin',
    createdAt: new Date('2025-01-01')
  }
];

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true
  });

  // Check for existing session on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('kms-user');
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser);
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false
        });
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('kms-user');
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    } else {
      setAuthState(prev => ({ ...prev, isLoading: false }));
    }
  }, []);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    const { username, password } = credentials;
    
    // Find user in demo users
    const demoUser = DEMO_USERS.find(
      u => u.username === username && u.password === password
    );

    if (demoUser) {
      const user: User = {
        id: demoUser.id,
        username: demoUser.username,
        role: demoUser.role,
        createdAt: demoUser.createdAt
      };

      setAuthState({
        user,
        isAuthenticated: true,
        isLoading: false
      });

      // Save to localStorage for persistence
      localStorage.setItem('kms-user', JSON.stringify(user));
      return true;
    }

    return false;
  };

  const logout = () => {
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false
    });
    localStorage.removeItem('kms-user');
  };

  const hasRole = (role: UserRole): boolean => {
    return authState.user?.role === role;
  };

  const canUploadDocuments = (): boolean => {
    return authState.user?.role === 'admin';
  };

  const contextValue: AuthContextType = {
    ...authState,
    login,
    logout,
    hasRole,
    canUploadDocuments
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
