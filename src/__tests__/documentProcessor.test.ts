import { DocumentProcessor } from '../utils/documentProcessor';

describe('DocumentProcessor', () => {
  describe('processFile', () => {
    it('should process text files correctly', async () => {
      const testContent = 'This is a test document. It has multiple sentences. This should be chunked properly.';
      const buffer = Buffer.from(testContent, 'utf-8');
      
      const result = await DocumentProcessor.processFile(buffer, 'test.txt');
      
      expect(result.content).toBe(testContent);
      expect(result.chunks).toHaveLength(1);
      expect(result.chunks[0]).toContain('This is a test document');
    });

    it('should throw error for unsupported file types', async () => {
      const buffer = Buffer.from('test content', 'utf-8');
      
      await expect(DocumentProcessor.processFile(buffer, 'test.xyz')).rejects.toThrow('Unsupported file type: xyz');
    });
  });

  describe('chunkText', () => {
    it('should chunk long text properly', () => {
      const longText = 'This is a sentence. '.repeat(100); // Create a long text
      const buffer = Buffer.from(longText, 'utf-8');
      
      return DocumentProcessor.processFile(buffer, 'test.txt').then(result => {
        expect(result.chunks.length).toBeGreaterThan(1);
        expect(result.chunks[0].length).toBeLessThanOrEqual(1000);
      });
    });

    it('should handle short text as single chunk', () => {
      const shortText = 'Short text.';
      const buffer = Buffer.from(shortText, 'utf-8');
      
      return DocumentProcessor.processFile(buffer, 'test.txt').then(result => {
        expect(result.chunks).toHaveLength(1);
        expect(result.chunks[0]).toBe(shortText);
      });
    });
  });
});

// Mock implementations for testing
jest.mock('pdf-parse', () => ({
  __esModule: true,
  default: jest.fn().mockResolvedValue({ text: 'Mocked PDF content' }),
}));

jest.mock('mammoth', () => ({
  extractRawText: jest.fn().mockResolvedValue({ value: 'Mocked DOCX content' }),
})); 