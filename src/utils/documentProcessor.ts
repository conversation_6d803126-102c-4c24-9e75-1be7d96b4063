// Dynamic import to avoid SSR issues with pdf-parse
import * as mammoth from 'mammoth';

export interface ProcessedDocument {
  content: string;
  chunks: string[];
}

export class DocumentProcessor {
  private static readonly CHUNK_SIZE = 1000; // Characters per chunk
  private static readonly CHUNK_OVERLAP = 200; // Overlap between chunks

  static async processFile(buffer: Buffer, filename: string): Promise<ProcessedDocument> {
    const extension = filename.split('.').pop()?.toLowerCase();
    let content = '';

    switch (extension) {
      case 'pdf':
        content = await this.processPDF(buffer);
        break;
      case 'txt':
        content = await this.processText(buffer);
        break;
      case 'docx':
        content = await this.processDocx(buffer);
        break;
      default:
        throw new Error(`Unsupported file type: ${extension}`);
    }

    const chunks = this.chunkText(content);
    return { content, chunks };
  }

  private static async processPDF(buffer: Buffer): Promise<string> {
    try {
      // Ensure we're passing the buffer correctly
      if (!buffer || buffer.length === 0) {
        throw new Error('Invalid or empty PDF buffer');
      }
      
      console.log('Processing PDF buffer of size:', buffer.length);
      
      // Use dynamic import with proper error handling
      const pdfParseModule = await import('pdf-parse');
      const pdfParse = pdfParseModule.default;
      
      if (typeof pdfParse !== 'function') {
        throw new Error('pdf-parse is not a function');
      }
      
      const data = await pdfParse(buffer);
      console.log('PDF processed successfully, text length:', data.text.length);
      return data.text;
    } catch (error) {
      console.error('Error processing PDF:', error);
      console.error('Buffer details:', buffer ? `Buffer size: ${buffer.length}` : 'Buffer is null/undefined');
      throw new Error(`Failed to process PDF file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private static async processText(buffer: Buffer): Promise<string> {
    try {
      return buffer.toString('utf-8');
    } catch (error) {
      console.error('Error processing text file:', error);
      throw new Error('Failed to process text file');
    }
  }

  private static async processDocx(buffer: Buffer): Promise<string> {
    try {
      const result = await mammoth.extractRawText({ buffer });
      return result.value;
    } catch (error) {
      console.error('Error processing DOCX:', error);
      throw new Error('Failed to process DOCX file');
    }
  }

  private static chunkText(text: string): string[] {
    const chunks: string[] = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    let currentChunk = '';
    
    for (const sentence of sentences) {
      const trimmedSentence = sentence.trim();
      if (!trimmedSentence) continue;
      
      const sentenceWithPunctuation = trimmedSentence + '.';
      
      // If adding this sentence would exceed chunk size, start a new chunk
      if (currentChunk.length + sentenceWithPunctuation.length > this.CHUNK_SIZE) {
        if (currentChunk.trim()) {
          chunks.push(currentChunk.trim());
        }
        
        // Start new chunk with overlap from previous chunk
        const words = currentChunk.split(' ');
        const overlapWords = words.slice(-Math.floor(this.CHUNK_OVERLAP / 6)); // Approximate word count
        currentChunk = overlapWords.join(' ') + ' ' + sentenceWithPunctuation;
      } else {
        currentChunk += (currentChunk ? ' ' : '') + sentenceWithPunctuation;
      }
    }
    
    // Add the last chunk if it has content
    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }
    
    // If no chunks were created (very short text), return the original text as one chunk
    if (chunks.length === 0 && text.trim()) {
      chunks.push(text.trim());
    }
    
    return chunks;
  }

  static async searchSimilarChunks(
    query: string,
    allChunks: Array<{ chunk_text: string; embedding: string; filename: string; id: number }>,
    topK: number = 5
  ): Promise<Array<{ chunk_text: string; filename: string; similarity: number; id: number }>> {
    const { getGeminiService } = await import('../lib/gemini');
    const geminiService = getGeminiService();
    
    // Generate embedding for the query
    const queryEmbedding = await geminiService.generateEmbedding(query);
    
    // Calculate similarities
    const similarities = allChunks.map(chunk => {
      const chunkEmbedding = JSON.parse(chunk.embedding);
      const similarity = geminiService.calculateCosineSimilarity(queryEmbedding, chunkEmbedding);
      
      return {
        chunk_text: chunk.chunk_text,
        filename: chunk.filename,
        similarity,
        id: chunk.id
      };
    });
    
    // Sort by similarity (descending) and return top K
    return similarities
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, topK);
  }
} 