export interface Document {
  id: number;
  filename: string;
  content: string;
  file_type: string;
  upload_date: string;
  file_size: number;
}

export interface DocumentChunk {
  id: number;
  document_id: number;
  chunk_text: string;
  chunk_index: number;
  embedding: string;
  created_at: string;
}

export interface DocumentChunkWithFilename extends DocumentChunk {
  filename: string;
}

export interface DatabaseRunResult {
  lastID: number;
  changes: number;
} 