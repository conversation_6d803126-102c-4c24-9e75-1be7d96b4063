export type UserRole = 'admin' | 'user';

export interface User {
  id: string;
  username: string;
  role: User<PERSON><PERSON>;
  createdAt: Date;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  hasRole: (role: UserRole) => boolean;
  canUploadDocuments: () => boolean;
}
