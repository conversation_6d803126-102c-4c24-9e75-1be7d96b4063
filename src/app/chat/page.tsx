'use client';

import { ChatInterface } from '../../components/ChatInterface';
import { useAuth } from '../../contexts/AuthContext';

export default function ChatPage() {
  const { user } = useAuth();

  return (
    <div className="flex-1 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-[#224d59] mb-2">
            💬 Ask Questions
          </h1>
          <p className="text-gray-600">
            Ask questions about your uploaded documents. The AI will provide answers
            with source references from your knowledge base.
          </p>
          {user && (
            <div className="mt-4 bg-green-50 border border-green-200 rounded-md p-3">
              <p className="text-sm text-green-800">
                <span className="font-medium">Welcome, {user.username}!</span> You can ask questions about any documents in the knowledge base.
              </p>
            </div>
          )}
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border-l-4 border-[#63ae45]">
          <ChatInterface />
        </div>
      </div>
    </div>
  );
}