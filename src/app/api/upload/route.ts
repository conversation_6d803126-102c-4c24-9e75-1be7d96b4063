import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '../../../lib/database';
import { getGeminiService } from '../../../lib/gemini';
import { DocumentProcessor } from '../../../utils/documentProcessor';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type
    const allowedTypes = ['application/pdf', 'text/plain', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'Unsupported file type' }, { status: 400 });
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Process the document
    const processedDoc = await DocumentProcessor.processFile(buffer, file.name);
    
    // Get database and Gemini service
    const db = getDatabase();
    const geminiService = getGeminiService();
    
    // Insert document into database
    const documentId = await db.insertDocument(
      file.name,
      processedDoc.content,
      file.type,
      file.size
    );
    
    // Process and store chunks with embeddings
    for (let i = 0; i < processedDoc.chunks.length; i++) {
      const chunk = processedDoc.chunks[i];
      const embedding = await geminiService.generateEmbedding(chunk);
      
      await db.insertDocumentChunk(
        documentId,
        chunk,
        i,
        JSON.stringify(embedding)
      );
    }
    
    return NextResponse.json({
      message: 'Document uploaded and processed successfully',
      documentId,
      chunksCount: processedDoc.chunks.length
    });
    
  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload and process document' },
      { status: 500 }
    );
  }
} 