import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '../../../lib/database';
import { getGeminiService } from '../../../lib/gemini';
import { DocumentProcessor } from '../../../utils/documentProcessor';

export async function POST(request: NextRequest) {
  try {
    const { question } = await request.json();
    
    if (!question || typeof question !== 'string') {
      return NextResponse.json({ error: 'Question is required' }, { status: 400 });
    }

    // Get database and retrieve all chunks
    const db = getDatabase();
    const allChunks = await db.getAllChunks();
    
    if (allChunks.length === 0) {
      return NextResponse.json({
        answer: "I don't have any documents to search through. Please upload some documents first.",
        sources: []
      });
    }

    // Find similar chunks
    const similarChunks = await DocumentProcessor.searchSimilarChunks(
      question,
      allChunks,
      5 // Top 5 most similar chunks
    );

    // Filter chunks with reasonable similarity (threshold: 0.1)
    const relevantChunks = similarChunks.filter(chunk => chunk.similarity > 0.1);
    
    if (relevantChunks.length === 0) {
      return NextResponse.json({
        answer: "I couldn't find relevant information in the uploaded documents to answer your question.",
        sources: []
      });
    }

    // Generate answer using Gemini
    const geminiService = getGeminiService();
    const contextTexts = relevantChunks.map(chunk => chunk.chunk_text);
    const answer = await geminiService.generateAnswer(question, contextTexts);

    // Prepare sources information
    const sources = relevantChunks.map(chunk => ({
      filename: chunk.filename,
      similarity: chunk.similarity,
      text: chunk.chunk_text.substring(0, 200) + '...' // Preview text
    }));

    return NextResponse.json({
      answer,
      sources,
      chunksUsed: relevantChunks.length
    });
    
  } catch (error) {
    console.error('Chat error:', error);
    return NextResponse.json(
      { error: 'Failed to process question' },
      { status: 500 }
    );
  }
} 