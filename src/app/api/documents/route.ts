import { NextResponse } from 'next/server';
import { getDatabase } from '../../../lib/database';

export async function GET() {
  try {
    // Add a small delay to ensure database is properly initialized
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const db = getDatabase();
    const documents = await db.getAllDocuments();
    
    return NextResponse.json({ documents });
  } catch (error) {
    console.error('Documents fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch documents' },
      { status: 500 }
    );
  }
} 