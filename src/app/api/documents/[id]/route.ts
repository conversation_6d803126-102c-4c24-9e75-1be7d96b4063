import { NextRequest, NextResponse } from 'next/server';
import { getDatabase } from '../../../../lib/database';

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const documentId = parseInt(params.id);
    
    if (isNaN(documentId)) {
      return NextResponse.json(
        { error: 'Invalid document ID' },
        { status: 400 }
      );
    }

    // Add a small delay to ensure database is properly initialized
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const db = getDatabase();
    await db.deleteDocument(documentId);
    
    return NextResponse.json({ 
      message: 'Document deleted successfully' 
    });
  } catch (error) {
    console.error('Document deletion error:', error);
    
    if (error instanceof Error && error.message === 'Document not found') {
      return NextResponse.json(
        { error: 'Document not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to delete document' },
      { status: 500 }
    );
  }
} 