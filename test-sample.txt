Knowledge Management System Test Document

This is a test document for the Knowledge Management System (KMS) demo.

The KMS system allows users to:
1. Upload documents in PDF, TXT, and DOCX formats
2. Process documents by chunking them into smaller pieces
3. Generate embeddings for semantic search
4. Ask questions about the uploaded documents
5. Receive AI-generated answers with source references

Key Features:
- Document upload and processing
- Semantic search using embeddings
- RAG (Retrieval-Augmented Generation) capabilities
- Integration with Gemini API
- SQLite database for storage
- Modern web interface built with Next.js

This system demonstrates how artificial intelligence can be used to create intelligent document search and question-answering systems.

The main components include:
- Document processor for handling different file types
- Database manager for SQLite operations
- Gemini service for AI functionality
- React components for the user interface
- API routes for backend functionality

Test Questions:
- What is the main purpose of this system?
- What file formats are supported?
- What AI model is used?
- How does the semantic search work? 