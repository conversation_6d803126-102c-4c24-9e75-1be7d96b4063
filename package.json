{"name": "kms-demo", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@types/multer": "^2.0.0", "@types/pdf-parse": "^1.1.5", "@types/sqlite3": "^3.1.11", "@types/uuid": "^10.0.0", "mammoth": "^1.9.1", "multer": "^2.0.1", "next": "15.3.5", "pdf-parse": "^1.1.1", "react": "^19.0.0", "react-dom": "^19.0.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "jest": "^30.0.4", "tailwindcss": "^4", "ts-jest": "^29.4.0", "typescript": "^5"}}